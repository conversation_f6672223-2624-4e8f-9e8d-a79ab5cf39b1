from flask import render_template, redirect, url_for, jsonify, request, flash, current_app, abort
from flask_login import login_required, current_user
from werkzeug.exceptions import NotFound
from app.utils.decorators import permission_required
from datetime import datetime, timedelta
from sqlalchemy import func

from app.modules.pos.models_sale import Sale, SaleItem, SaleStatus, Payment
from app.modules.inventory.models_product import Product, ProductCategory
from app.modules.customers.models import Customer
from app.modules.tables.models_table import Table, TableStatus, Room
from app.modules.cash_register.models_cash_register import CashRegister, CashOperation, CashRegisterOperationType, CashOutReason, PaymentMethod
from app.modules.inventory.models_stock_movement import StockMovementReason
from app.modules.settings.models_settings import Settings
from app.extensions import db
from . import bp

@bp.route('/')
@login_required
@permission_required('can_process_sales')
def index():
    products = Product.query.filter_by(owner_id=current_user.id).all()
    categories = ProductCategory.query.filter_by(owner_id=current_user.id).all()
    # Récupérer les tables disponibles pour la sélection
    tables = Table.query.filter_by(owner_id=current_user.id).order_by(Table.number).all()
    # Récupérer les salles disponibles
    rooms = Room.query.filter_by(owner_id=current_user.id, is_active=True).order_by(Room.name).all()

    # Vérifier s'il y a une commande à éditer
    edit_order_id = request.args.get('edit_order')
    edit_order = None
    if edit_order_id:
        edit_order = Sale.query.filter_by(id=edit_order_id, owner_id=current_user.id).first()
        if not edit_order:
            flash('Commande non trouvée ou accès non autorisé.', 'error')

    return render_template('pos/index.html',
                         products=products,
                         categories=categories,
                         tables=tables,
                         rooms=rooms,
                         edit_order=edit_order)

@bp.route('/sales')
@login_required
@permission_required('can_process_sales')
def sales():
    """Liste des ventes"""
    page = request.args.get('page', 1, type=int)
    status = request.args.get('status', None)
    date_from = request.args.get('date_from', None)
    date_to = request.args.get('date_to', None)

    # Base query
    query = Sale.query.filter_by(owner_id=current_user.id).order_by(Sale.created_at.desc())

    # Apply status filter if provided
    if status and status in [s.name for s in SaleStatus]:
        query = query.filter_by(status=SaleStatus[status])

    # Apply date filters if provided
    if date_from:
        try:
            date_from = datetime.strptime(date_from, '%Y-%m-%d')
            query = query.filter(Sale.created_at >= date_from)
        except ValueError:
            pass

    if date_to:
        try:
            date_to = datetime.strptime(date_to, '%Y-%m-%d')
            query = query.filter(Sale.created_at <= date_to + timedelta(days=1))
        except ValueError:
            pass

    # Get paginated results
    sales = query.paginate(page=page, per_page=10, error_out=False)

    # Create list of possible statuses for filter
    statuses = [(s.name, s.value) for s in SaleStatus]

    return render_template('pos/sales.html',
                         sales=sales,
                         status=status,
                         date_from=date_from,
                         date_to=date_to,
                         statuses=statuses)

@bp.route('/sales/<int:id>')
@login_required
@permission_required('can_process_sales')
def sale_details(id):
    """Détails d'une vente"""
    sale = Sale.query.get_or_404(id)
    if sale.owner_id != current_user.id:
        abort(403)
    return render_template('pos/sale_details.html', sale=sale)

@bp.route('/payment/<int:sale_id>')
@login_required
@permission_required('can_process_sales')
def payment_form(sale_id):
    """Formulaire de paiement pour une vente"""
    sale = Sale.query.get_or_404(sale_id)
    if sale.owner_id != current_user.id:
        abort(403)

    # Vérifier si la vente peut être payée
    if sale.status not in [SaleStatus.PENDING, SaleStatus.KITCHEN_READY]:
        flash('Cette vente ne peut pas être payée dans son état actuel.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

    return render_template('pos/payment_form.html', sale=sale)

@bp.route('/sales/daily')
@login_required
@permission_required('can_process_sales')
def daily_sales():
    """Récapitulatif des ventes du jour"""
    today = datetime.now().date()

    # Get all paid sales for today
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).all()

    # Calculate totals by payment method
    payment_totals = {}
    for method in PaymentMethod:
        payment_totals[method.name] = sum(
            payment.amount for sale in sales for payment in sale.payments
            if payment.method == method
        )

    # Calculate total sales
    total_sales = len(sales)  # Nombre de ventes
    total_revenue = sum(sale.total for sale in sales)  # Chiffre d'affaires
    total_items = sum(sum(item.quantity for item in sale.items) for sale in sales)
    average_sale = total_revenue / total_sales if total_sales > 0 else 0  # Panier moyen

    # Get hourly sales data for chart
    hourly_sales = db.session.query(
        func.strftime('%H', Sale.created_at).label('hour'),
        func.sum(Sale.total).label('total')
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).group_by('hour').all()

    hourly_data = [0] * 24
    for hour, total in hourly_sales:
        hourly_data[int(hour)] = float(total or 0)

    # Produits les plus vendus aujourd'hui
    top_products = db.session.query(
        Product,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.price * SaleItem.quantity).label('total_amount')
    ).join(
        SaleItem, SaleItem.product_id == Product.id
    ).join(
        Sale, Sale.id == SaleItem.sale_id
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        func.date(Sale.created_at) == today
    ).group_by(
        Product.id
    ).order_by(
        func.sum(SaleItem.price * SaleItem.quantity).desc()
    ).limit(5).all()

    return render_template('pos/daily_sales.html',
                         sales=sales,
                         payment_totals=payment_totals,
                         total_sales=total_sales,
                         total_revenue=total_revenue,
                         total_items=total_items,
                         average_sale=average_sale,
                         hourly_data=hourly_data,
                         top_products=top_products,
                         title="Ventes du jour")

@bp.route('/sales/stats')
@login_required
@permission_required('can_process_sales')
def sales_stats():
    """Statistiques des ventes"""
    # Récupérer les paramètres de filtre
    period = request.args.get('period', 'day')
    start_date_str = request.args.get('start_date')
    end_date_str = request.args.get('end_date')

    # Convertir les dates si fournies
    today = datetime.now().date()

    if start_date_str:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today
    else:
        # Définir la date de début par défaut selon la période
        if period == 'day':
            start_date = today
        elif period == 'week':
            start_date = today - timedelta(days=6)  # Derniers 7 jours
        elif period == 'month':
            start_date = today.replace(day=1)  # Premier jour du mois
        elif period == 'year':
            start_date = today.replace(month=1, day=1)  # Premier jour de l'année
        else:
            start_date = today

    if end_date_str:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            end_date = today
    else:
        end_date = today

    # Assurer que end_date est après start_date
    if end_date < start_date:
        end_date = start_date

    # Créer les objets datetime pour la requête
    start_datetime = datetime.combine(start_date, datetime.min.time())
    end_datetime = datetime.combine(end_date, datetime.max.time())

    # Requête de base pour les ventes payées
    sales_query = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        Sale.created_at >= start_datetime,
        Sale.created_at <= end_datetime
    )

    # Récupérer toutes les ventes dans la période
    sales = sales_query.all()

    # Calculer les totaux par méthode de paiement
    payment_totals = {}
    for method in PaymentMethod:
        payment_totals[method.name] = sum(
            payment.amount for sale in sales for payment in sale.payments
            if payment.method == method
        )

    # Calculer le total des ventes et le nombre d'articles
    total_sales = len(sales)  # Nombre de ventes
    total_revenue = sum(sale.total for sale in sales)  # Chiffre d'affaires
    total_items = sum(sum(item.quantity for item in sale.items) for sale in sales)
    average_sale = total_revenue / total_sales if total_sales > 0 else 0  # Panier moyen

    # Calculer les ventes par jour pour le graphique
    daily_sales = {}
    for sale in sales:
        sale_date = sale.created_at.date()
        if sale_date not in daily_sales:
            daily_sales[sale_date] = 0
        daily_sales[sale_date] += sale.total

    # Préparer les données du graphique
    dates = []
    values = []

    # Remplir toutes les dates dans la plage, même celles sans ventes
    current_date = start_date
    while current_date <= end_date:
        dates.append(current_date.strftime('%Y-%m-%d'))
        values.append(daily_sales.get(current_date, 0))
        current_date += timedelta(days=1)

    # Calculer les ventes par heure pour aujourd'hui
    hourly_sales = {}
    if period == 'day' or period == 'today':
        for hour in range(24):
            hourly_sales[hour] = 0

        for sale in sales:
            if sale.created_at.date() == today:
                hour = sale.created_at.hour
                hourly_sales[hour] += sale.total

    # Calculer les ventes par jour de la semaine
    weekly_sales = []
    days_of_week = ['Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi', 'Dimanche']
    weekly_totals = [0] * 7

    for sale in sales:
        day_idx = sale.created_at.weekday()  # 0 = Lundi, 6 = Dimanche
        weekly_totals[day_idx] += sale.total

    for i, day in enumerate(days_of_week):
        weekly_sales.append([day, weekly_totals[i]])

    # Calculer les ventes par mois
    monthly_sales = []
    months = ['Janvier', 'Février', 'Mars', 'Avril', 'Mai', 'Juin',
              'Juillet', 'Août', 'Septembre', 'Octobre', 'Novembre', 'Décembre']
    monthly_totals = [0] * 12

    for sale in sales:
        month_idx = sale.created_at.month - 1  # 0 = Janvier, 11 = Décembre
        monthly_totals[month_idx] += sale.total

    for i, month in enumerate(months):
        monthly_sales.append([month, monthly_totals[i]])

    # Calculer les ventes par année
    yearly_sales = []
    current_year = datetime.now().year
    years = list(range(current_year - 4, current_year + 1))  # 5 dernières années
    yearly_totals = {year: 0 for year in years}

    for sale in sales:
        year = sale.created_at.year
        if year in yearly_totals:
            yearly_totals[year] += sale.total

    for year in sorted(yearly_totals.keys()):
        yearly_sales.append([str(year), yearly_totals[year]])

    # Créer un dictionnaire de ventes par jour (pour périodes personnalisées)
    sales_by_day = {}
    for date_str, value in zip(dates, values):
        sales_by_day[date_str] = value

    # Produits les plus vendus
    top_products = db.session.query(
        Product.name,
        func.sum(SaleItem.quantity).label('total_quantity'),
        func.sum(SaleItem.price * SaleItem.quantity).label('total_amount')
    ).join(
        SaleItem, SaleItem.product_id == Product.id
    ).join(
        Sale, Sale.id == SaleItem.sale_id
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.PAID,
        Sale.created_at >= start_datetime,
        Sale.created_at <= end_datetime
    ).group_by(
        Product.id
    ).order_by(
        func.sum(SaleItem.price * SaleItem.quantity).desc()
    ).limit(10).all()

    return render_template('pos/sales_stats.html',
                         period=period,
                         start_date=start_date,
                         end_date=end_date,
                         payment_totals=payment_totals,
                         total_sales=total_sales,
                         total_revenue=total_revenue,
                         total_items=total_items,
                         average_sale=average_sale,
                         dates=dates,
                         values=values,
                         top_products=top_products,
                         sales_count=len(sales),
                         hourly_sales=hourly_sales,
                         weekly_sales=weekly_sales,
                         monthly_sales=monthly_sales,
                         yearly_sales=yearly_sales,
                         sales_by_day=sales_by_day)

@bp.route('/send_to_kitchen', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def send_to_kitchen():
    """Send order to kitchen without payment"""
    try:
        data = request.get_json()
        items = data.get('items', [])
        table_id = data.get('table_id')
        kitchen_note = data.get('kitchen_note')
        covers_count = data.get('covers_count', 1)
        service_type = data.get('service_type', 'dine_in')

        if not items:
            return jsonify({'success': False, 'error': 'Aucun article dans la commande'})

        current_app.logger.info("Création d'une nouvelle commande pour la cuisine...")

        # Create sale record with kitchen pending status
        sale = Sale(
            owner_id=current_user.id,
            user_id=current_user.id,
            status=SaleStatus.KITCHEN_PENDING,
            kitchen_status='pending',
            table_id=table_id,
            table_number=table_id,  # Garde pour compatibilité
            kitchen_note=kitchen_note,
            covers_count=covers_count,
            service_type=service_type
        )

        db.session.add(sale)
        db.session.flush()  # Pour obtenir l'ID de la vente

        # Occuper la table si une table est sélectionnée
        if table_id:
            table = Table.query.get(table_id)
            if table and table.is_available():
                table.occupy(sale.id, covers_count, commit=False)

        # Add items to sale
        subtotal = 0
        current_app.logger.info(f"Ajout de {len(items)} articles à la commande...")

        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = item_data.get('quantity')
            price = item_data.get('price')

            product = Product.query.get(product_id)
            if not product:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Produit {product_id} non trouvé'})

            if product.owner_id != current_user.id:
                db.session.rollback()
                return jsonify({'success': False, 'error': 'Accès non autorisé au produit'})

            # Create sale item
            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product_id,
                quantity=quantity,
                price=price,
                total=price * quantity
            )
            db.session.add(sale_item)
            subtotal += sale_item.total

        # Calculate totals
        sale.subtotal = subtotal
        sale.total = subtotal  # Pas de taxes pour l'instant

        db.session.commit()

        return jsonify({
            'success': True,
            'sale_id': sale.id,
            'message': 'Commande envoyée à la cuisine avec succès'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors de l'envoi à la cuisine: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Erreur lors de l\'envoi: {str(e)}'})

@bp.route('/process_pos_payment', methods=['POST'])
@login_required
def process_pos_payment():
    """Traitement du paiement depuis le POS"""
    # Get form data
    order_data = request.json

    if not order_data:
        return jsonify({'success': False, 'error': 'Données de commande invalides'})

    items = order_data.get('items', [])
    payments = order_data.get('payments', [])

    if not items:
        return jsonify({'success': False, 'error': 'La commande ne contient pas d\'articles'})

    if not payments:
        return jsonify({'success': False, 'error': 'Aucun paiement fourni'})

    # Get additional data
    table_id = order_data.get('table_id')
    customer_id = order_data.get('customer_id')
    discount_percentage = order_data.get('discount_percentage', 0)
    kitchen_note = order_data.get('kitchen_note', '')

    # Check if we have an open cash register
    cash_register = CashRegister.get_open_register(owner_id=current_user.id)
    if not cash_register:
        return jsonify({'success': False, 'error': 'Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.'})

    try:
        # Create sale record
        current_app.logger.info("Création d'une nouvelle vente...")
        sale = Sale(
            owner_id=current_user.id,
            user_id=current_user.id,
            status=SaleStatus.PAID,
            table_id=table_id,
            table_number=table_id,  # Garde pour compatibilité
            customer_id=customer_id,
            kitchen_note=kitchen_note
        )

        db.session.add(sale)
        db.session.flush()  # Pour obtenir l'ID de la vente

        # Occuper la table si une table est sélectionnée
        if table_id:
            table = Table.query.get(table_id)
            if table and table.is_available():
                table.occupy(sale.id, commit=False)

        # Add items to sale
        subtotal = 0
        current_app.logger.info(f"Ajout de {len(items)} articles au panier...")

        for item_data in items:
            product_id = item_data.get('product_id')
            quantity = item_data.get('quantity', 1)
            price = item_data.get('price')

            # Verify product exists
            product = Product.query.get(product_id)
            if not product:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Produit non trouvé: {product_id}'})

            # Verify we have enough stock if stock tracking is available
            if hasattr(product, 'stock_quantity') and product.stock_quantity is not None:
                if product.stock_quantity < quantity:
                    db.session.rollback()
                    return jsonify({'success': False, 'error': f'Stock insuffisant pour {product.name}'})

            # Add item to sale
            sale_item = SaleItem(
                sale=sale,
                product_id=product_id,
                quantity=quantity,
                price=price,
                total=price * quantity
            )
            db.session.add(sale_item)

            # Update stock
            try:
                current_app.logger.info(f"Mise à jour du stock pour {product.name}, quantité: {quantity}")

                # Cas 1: Produit avec recette
                if hasattr(product, 'recipe') and product.recipe is not None:
                    current_app.logger.info(f"Produit avec recette: {product.name}")
                    for recipe_item in product.recipe.items:
                        ingredient = recipe_item.ingredient
                        ingredient_qty_to_deduct = recipe_item.quantity * quantity
                        current_app.logger.info(f"Déduction de {ingredient_qty_to_deduct} de l'ingrédient {ingredient.name}")
                        ingredient.stock_quantity -= ingredient_qty_to_deduct

                # Cas 2: Produit simple - mise à jour directe du stock
                else:
                    current_app.logger.info(f"Mise à jour directe du stock pour {product.name}")
                    if hasattr(product, 'stock_quantity'):
                        product.stock_quantity -= quantity
                        current_app.logger.info(f"Nouveau stock: {product.stock_quantity}")

                # S'assurer que les modifications sont sauvegardées
                db.session.flush()

            except Exception as e:
                current_app.logger.error(f"Erreur lors de la mise à jour du stock: {str(e)}", exc_info=True)
                # Continue processing - don't stop the sale for stock issues

            # Add to subtotal
            subtotal += price * quantity

        # Calculate total with discount
        if discount_percentage > 0:
            discount_amount = (subtotal * discount_percentage) / 100
            sale.discount_amount = discount_amount
            total = subtotal - discount_amount
        else:
            total = subtotal

        # Ensure total matches the sum of payments
        payment_total = sum(payment.get('amount', 0) for payment in payments)
        if abs(payment_total - total) > 0.01:  # Allow for small rounding differences
            db.session.rollback()
            return jsonify({
                'success': False,
                'error': f'Le total des paiements ({payment_total}) ne correspond pas au total de la vente ({total})'
            })

        # Set sale totals
        sale.subtotal = subtotal
        sale.total = total

        # Process payments
        for payment_data in payments:
            payment_method_name = payment_data.get('method')
            amount = payment_data.get('amount')

            try:
                payment_method = PaymentMethod[payment_method_name]
            except KeyError:
                db.session.rollback()
                return jsonify({'success': False, 'error': f'Méthode de paiement invalide: {payment_method_name}'})

            # Add payment record
            payment = Payment(
                sale=sale,
                method=payment_method,
                amount=amount
            )
            db.session.add(payment)

            # Record cash register operation for this payment
            # Récupérer le numéro de table si une table est sélectionnée
            table_display = None
            if table_id:
                table = Table.query.get(table_id)
                if table:
                    table_display = table.number

            operation = CashOperation(
                register_id=cash_register.id,
                type=CashRegisterOperationType.SALE,
                amount=amount,
                payment_method=payment_method,
                user_id=current_user.id,
                owner_id=current_user.id,
                note=f"Vente #{sale.id}",
                table_number=table_display
            )
            db.session.add(operation)

        # Update table status if applicable
        if table_id:
            table = Table.query.get(table_id)
            if table:
                table.reset_table()

        db.session.commit()

        # Return success response with sale ID
        return jsonify({
            'success': True,
            'sale_id': sale.id,
            'message': 'Vente enregistrée avec succès'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors du traitement du paiement: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Erreur lors du traitement: {str(e)}'})

@bp.route('/process_payment', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def process_payment():
    """Process payment for a sale"""
    sale_id = request.form.get('sale_id')
    payment_method = request.form.get('payment_method')
    amount_tendered = float(request.form.get('amount_tendered', 0))

    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Check if sale is already paid
    if sale.status == SaleStatus.PAID:
        flash('Cette vente a déjà été payée.', 'warning')
        return redirect(url_for('pos.sale_details', id=sale_id))

    # Check if we have an open cash register
    cash_register = CashRegister.get_open_register(owner_id=current_user.id)
    if not cash_register:
        flash('Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.', 'error')
        return redirect(url_for('cash_register.open'))

    # Calculate change
    amount_due = sale.total
    change_amount = max(0, amount_tendered - amount_due) if amount_tendered > 0 else 0

    try:
        # Set sale as paid
        sale.status = SaleStatus.PAID
        sale.paid_at = datetime.utcnow()
        sale.cash_register_id = cash_register.id

        # Create payment record
        payment = Payment(
            sale=sale,
            method=PaymentMethod[payment_method],
            amount=amount_due
        )
        db.session.add(payment)

        # Record cash register operation
        # Récupérer le numéro de table si une table est associée
        table_display = None
        if sale.table:
            table_display = sale.table.number

        operation = CashOperation(
            register_id=cash_register.id,
            type=CashRegisterOperationType.SALE,
            amount=amount_due,
            payment_method=PaymentMethod[payment_method],
            user_id=current_user.id,
            owner_id=current_user.id,
            note=f"Vente #{sale.id}",
            table_number=table_display
        )
        db.session.add(operation)

        # Update product stock
        for item in sale.items:
            product = item.product
            if product and hasattr(product, 'stock_quantity') and hasattr(product, 'update_stock'):
                try:
                    product.update_stock(item.quantity, operation='subtract', reason=StockMovementReason.SALE, reference=f"Sale #{sale.id}")
                except Exception as e:
                    current_app.logger.warning(f"Erreur lors de la mise à jour du stock pour {product.name}: {str(e)}")

        # Update table status if applicable
        if sale.table:
            sale.table.reset_table()

        db.session.commit()

        flash('Paiement traité avec succès.', 'success')

        # Check if we should print a receipt
        settings = Settings.query.filter_by(owner_id=current_user.id).first()
        auto_print = settings.auto_print_receipt if settings else False

        # Check if request came from ready_orders page
        referrer = request.referrer
        if referrer and 'ready_orders' in referrer:
            flash('Paiement traité avec succès.', 'success')
            return redirect(url_for('pos.ready_orders'))

        if auto_print:
            return redirect(url_for('pos.print_receipt', sale_id=sale.id))
        else:
            return redirect(url_for('pos.sale_details', id=sale.id))

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors du traitement du paiement: {str(e)}', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

@bp.route('/cancel_sale/<int:sale_id>', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def cancel_sale(sale_id):
    """Annuler une vente"""
    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Only allow cancellation of pending or kitchen pending sales
    if sale.status not in [SaleStatus.PENDING, SaleStatus.KITCHEN_PENDING]:
        flash('Seules les ventes en attente peuvent être annulées.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

    try:
        # Mark as cancelled
        sale.status = SaleStatus.CANCELLED
        sale.cancelled_at = datetime.utcnow()
        sale.cancelled_by_id = current_user.id

        # Free up the table if applicable
        if sale.table:
            sale.table.reset_table()

        db.session.commit()
        flash('Vente annulée avec succès.', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur lors de l\'annulation de la vente: {str(e)}', 'error')

    return redirect(url_for('pos.sales'))

@bp.route('/print_receipt/<int:sale_id>')
@login_required
@permission_required('can_process_sales')
def print_receipt(sale_id):
    """Imprimer un reçu de vente"""
    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Get store settings
    settings = Settings.query.filter_by(owner_id=current_user.id).first()

    return render_template('pos/receipt.html',
                         sale=sale,
                         settings=settings)

@bp.route('/get_products/<int:category_id>')
@login_required
@permission_required('can_process_sales')
def get_products(category_id):
    """Get products by category for POS interface"""
    if category_id == 0:  # All products
        products = Product.query.filter_by(owner_id=current_user.id, is_active=True).all()
    else:
        products = Product.query.filter_by(
            owner_id=current_user.id,
            category_id=category_id,
            is_active=True
        ).all()

    products_data = []
    for product in products:
        # Calculate stock status
        stock_status = 'in_stock'
        if hasattr(product, 'stock_quantity') and product.stock_quantity is not None:
            stock_status = 'out_of_stock' if product.stock_quantity <= 0 else 'in_stock'

        products_data.append({
            'id': product.id,
            'name': product.name,
            'price': product.price,  # Utiliser 'price' au lieu de 'selling_price'
            'image_url': product.image_path or '/static/images/no-image.png',  # Utiliser 'image_path'
            'stock_status': stock_status
        })

    return jsonify({'products': products_data})

@bp.route('/kitchen')
@login_required
@permission_required('can_access_kitchen')
def kitchen_orders():
    """Kitchen display system - show orders that need to be prepared"""
    # Get kitchen pending sales with unprepared items
    sales = Sale.query.filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.KITCHEN_PENDING
    ).order_by(Sale.created_at).all()

    # Filter further in Python code to avoid SQL attribute errors
    # in case the column doesn't exist yet in the database
    filtered_sales = []
    for sale in sales:
        try:
            if sale.kitchen_status != 'completed':
                filtered_sales.append(sale)
        except:
            # If kitchen_status doesn't exist yet, include all pending sales
            filtered_sales.append(sale)

    return render_template('pos/kitchen.html', orders=filtered_sales)

@bp.route('/ready_orders')
@login_required
@permission_required('can_process_sales')
def ready_orders():
    """Display orders ready for service"""
    # Get orders that are ready to be served with preloaded table relationship
    # Note: Sale.items is a dynamic relationship, so we can't use joinedload on it
    ready_sales = Sale.query.options(
        db.joinedload(Sale.table)
    ).filter(
        Sale.owner_id == current_user.id,
        Sale.status == SaleStatus.KITCHEN_READY,
        Sale.kitchen_status == 'ready'
    ).order_by(Sale.created_at).all()

    return render_template('pos/ready_orders.html', orders=ready_sales)

@bp.route('/process_ready_payment', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def process_ready_payment():
    """Process payment for a ready order via AJAX"""
    try:
        current_app.logger.info("=== DEBUT process_ready_payment ===")
        current_app.logger.info(f"Request method: {request.method}")
        current_app.logger.info(f"Request content type: {request.content_type}")
        current_app.logger.info(f"Request data: {request.data}")

        data = request.get_json()
        current_app.logger.info(f"Parsed JSON data: {data}")

        if not data:
            current_app.logger.error("No JSON data received")
            return jsonify({'success': False, 'error': 'Aucune donnée reçue'})

        sale_id = data.get('sale_id')
        payment_method = data.get('payment_method')
        amount_tendered = float(data.get('amount_tendered', 0))

        current_app.logger.info(f"sale_id: {sale_id}, payment_method: {payment_method}, amount_tendered: {amount_tendered}")

        sale = Sale.query.get_or_404(sale_id)

        # Check ownership
        if sale.owner_id != current_user.id:
            return jsonify({'success': False, 'error': 'Accès non autorisé'})

        # Check if sale is already paid
        if sale.status == SaleStatus.PAID:
            return jsonify({'success': False, 'error': 'Cette vente a déjà été payée'})

        # Check if we have an open cash register
        cash_register = CashRegister.get_open_register(owner_id=current_user.id)
        if not cash_register:
            return jsonify({'success': False, 'error': 'Aucune caisse n\'est ouverte. Veuillez ouvrir une caisse avant de traiter des paiements.'})

        # Calculate change
        amount_due = sale.total
        change_amount = max(0, amount_tendered - amount_due) if amount_tendered > 0 else 0

        # Set sale as paid
        sale.status = SaleStatus.PAID
        sale.paid_at = datetime.utcnow()
        sale.cash_register_id = cash_register.id

        # Create payment record
        payment = Payment(
            sale=sale,
            method=PaymentMethod[payment_method],
            amount=amount_due
        )
        db.session.add(payment)

        # Record cash register operation
        table_display = None
        if sale.table:
            table_display = sale.table.number

        operation = CashOperation(
            register_id=cash_register.id,
            type=CashRegisterOperationType.SALE,
            amount=amount_due,
            payment_method=PaymentMethod[payment_method],
            user_id=current_user.id,
            owner_id=current_user.id,
            note=f"Vente #{sale.id}",
            table_number=table_display
        )
        db.session.add(operation)

        # Update product stock
        for item in sale.items:
            product = item.product
            if product and hasattr(product, 'stock_quantity') and hasattr(product, 'update_stock'):
                try:
                    product.update_stock(item.quantity, operation='subtract', reason='SALE', reference=f"Sale #{sale.id}")
                except Exception as e:
                    current_app.logger.warning(f"Erreur lors de la mise à jour du stock pour {product.name}: {str(e)}")

        # Update table status if applicable
        if sale.table:
            sale.table.reset_table()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Paiement traité avec succès',
            'change': change_amount
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"Erreur lors du traitement du paiement: {str(e)}", exc_info=True)
        return jsonify({'success': False, 'error': f'Erreur lors du traitement: {str(e)}'})

@bp.route('/kitchen/order/<int:order_id>/ready', methods=['POST'])
@login_required
@permission_required('can_access_kitchen')
def mark_order_ready(order_id):
    """Mark a kitchen order as ready"""
    sale = Sale.query.get_or_404(order_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        return jsonify({'success': False, 'error': 'Accès non autorisé'})

    try:
        # Update kitchen status and sale status
        sale.kitchen_status = 'ready'
        sale.status = SaleStatus.KITCHEN_READY

        db.session.commit()
        return jsonify({'success': True, 'message': 'Commande marquée comme prête'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/kitchen/order/<int:order_id>/delivered', methods=['POST'])
@login_required
@permission_required('can_access_kitchen')
def mark_order_delivered(order_id):
    """Mark a kitchen order as delivered"""
    sale = Sale.query.get_or_404(order_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        return jsonify({'success': False, 'error': 'Accès non autorisé'})

    try:
        # Update kitchen status and sale status
        sale.kitchen_status = 'delivered'
        sale.status = SaleStatus.DELIVERED

        db.session.commit()
        return jsonify({'success': True, 'message': 'Commande marquée comme livrée'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)})

@bp.route('/sale/complete/<int:sale_id>', methods=['POST'])
@login_required
def complete_sale(sale_id):
    """Mark a sale as complete after delivery"""
    sale = Sale.query.get_or_404(sale_id)

    # Check ownership
    if sale.owner_id != current_user.id:
        flash('Accès non autorisé.', 'error')
        return redirect(url_for('pos.index'))

    # Check if the sale can be completed
    if sale.status != SaleStatus.PAID:
        flash('Seules les ventes payées peuvent être marquées comme complétées.', 'error')
        return redirect(url_for('pos.sale_details', id=sale_id))

    try:
        # Mark as complete
        sale.status = SaleStatus.COMPLETED
        sale.completed_at = datetime.utcnow()

        db.session.commit()
        flash('Vente marquée comme complétée.', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'Erreur: {str(e)}', 'error')

    return redirect(url_for('pos.sale_details', id=sale_id))

@bp.route('/api/get_rooms')
@login_required
@permission_required('can_process_sales')
def get_rooms():
    """API pour récupérer les salles disponibles"""
    try:
        rooms = Room.query.filter_by(owner_id=current_user.id, is_active=True).order_by(Room.name).all()
        rooms_data = []

        for room in rooms:
            rooms_data.append({
                'id': room.id,
                'name': room.name,
                'description': room.description,
                'width': room.width,
                'height': room.height,
                'background_color': room.background_color,
                'is_default': room.is_default,
                'table_count': room.table_count,
                'occupied_tables_count': room.occupied_tables_count,
                'available_tables_count': room.available_tables_count
            })

        return jsonify(rooms_data)

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/get_table_order/<int:table_id>')
@login_required
@permission_required('can_process_sales')
def get_table_order(table_id):
    """API pour récupérer la commande en cours d'une table"""
    try:
        table = Table.query.get_or_404(table_id)
        if table.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        if not table.current_order_id:
            return jsonify({'success': False, 'error': 'Aucune commande en cours pour cette table'})

        sale = Sale.query.get(table.current_order_id)
        if not sale:
            return jsonify({'success': False, 'error': 'Commande introuvable'})

        # Préparer les données de la commande
        order_data = {
            'id': sale.id,
            'table_id': table.id,
            'table_name': table.display_name,
            'covers_count': sale.covers_count,
            'service_type': sale.service_type,
            'service_type_display': sale.service_type_display,
            'status': sale.status.value,
            'kitchen_status': sale.kitchen_status,
            'kitchen_note': sale.kitchen_note,
            'subtotal': sale.subtotal,
            'tax_amount': sale.tax_amount,
            'total': sale.total,
            'total_paid': sale.total_paid,
            'remaining_amount': sale.remaining_amount,
            'created_at': sale.created_at.isoformat() if sale.created_at else None,
            'items': []
        }

        # Ajouter les articles
        for item in sale.items:
            order_data['items'].append({
                'id': item.id,
                'product_id': item.product_id,
                'product_name': item.product.name if item.product else 'Produit supprimé',
                'quantity': item.quantity,
                'unit_price': item.price,  # Utiliser 'price' au lieu de 'unit_price'
                'total_price': item.total,  # Utiliser 'total' au lieu de 'total_price'
                'notes': getattr(item, 'notes', '')  # Vérifier si l'attribut existe
            })

        return jsonify({'success': True, 'order': order_data})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/update_order_covers', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def update_order_covers():
    """API pour mettre à jour le nombre de couverts d'une commande"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        new_covers = data.get('covers_count')

        if not sale_id or not new_covers:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Mettre à jour les couverts
        old_covers = sale.covers_count
        sale.covers_count = new_covers

        # Mettre à jour la table aussi
        if sale.table_id:
            table = Table.query.get(sale.table_id)
            if table:
                table.current_covers = new_covers

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'Couverts mis à jour de {old_covers} à {new_covers}',
            'old_covers': old_covers,
            'new_covers': new_covers
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/add_items_to_order', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def add_items_to_order():
    """API pour ajouter des articles à une commande existante"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        items = data.get('items', [])
        kitchen_note = data.get('kitchen_note', '')

        if not sale_id or not items:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Ajouter les nouveaux articles
        new_items = []
        for item_data in items:
            product = Product.query.get(item_data['product_id'])
            if not product:
                continue

            sale_item = SaleItem(
                sale_id=sale.id,
                product_id=product.id,
                quantity=item_data['quantity'],
                price=product.price,  # Utiliser 'price' au lieu de 'unit_price'
                total=product.price * item_data['quantity']  # Utiliser 'total' au lieu de 'total_price'
                # Retirer 'notes' car il n'existe pas dans le modèle SaleItem
            )
            db.session.add(sale_item)
            new_items.append(sale_item)

        # Mettre à jour la note cuisine si fournie
        if kitchen_note:
            sale.kitchen_note = kitchen_note

        # Recalculer les totaux
        sale.calculate_totals()

        db.session.commit()

        return jsonify({
            'success': True,
            'message': f'{len(new_items)} article(s) ajouté(s) à la commande',
            'new_total': sale.total,
            'items_added': len(new_items)
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/update_order_item', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def update_order_item():
    """API pour modifier la quantité d'un article dans une commande"""
    try:
        data = request.get_json()
        item_id = data.get('item_id')
        new_quantity = data.get('quantity')

        if not item_id or new_quantity is None:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale_item = SaleItem.query.get_or_404(item_id)
        sale = sale_item.sale

        if sale.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        if new_quantity <= 0:
            # Supprimer l'article
            db.session.delete(sale_item)
            message = f"Article '{sale_item.product.name}' supprimé de la commande"
        else:
            # Mettre à jour la quantité
            old_quantity = sale_item.quantity
            sale_item.quantity = new_quantity
            sale_item.total = sale_item.price * new_quantity  # Utiliser 'total' et 'price'
            message = f"Quantité de '{sale_item.product.name}' mise à jour de {old_quantity} à {new_quantity}"

        # Recalculer les totaux
        sale.calculate_totals()
        db.session.commit()

        return jsonify({
            'success': True,
            'message': message,
            'new_total': sale.total
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/move_order_to_table', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def move_order_to_table():
    """API pour déplacer une commande vers une autre table"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        new_table_id = data.get('new_table_id')

        if not sale_id or not new_table_id:
            return jsonify({'success': False, 'error': 'Données manquantes'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        old_table = Table.query.get(sale.table_id) if sale.table_id else None
        new_table = Table.query.get_or_404(new_table_id)

        if new_table.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Table non autorisée'}), 403

        # Vérifier que la nouvelle table est disponible
        if not new_table.is_available():
            return jsonify({'success': False, 'error': 'La table de destination n\'est pas disponible'})

        # Libérer l'ancienne table
        if old_table:
            old_table.reset_table(commit=False)

        # Occuper la nouvelle table
        new_table.occupy(sale.id, sale.covers_count, commit=False)

        # Mettre à jour la vente
        sale.table_id = new_table_id
        sale.table_number = new_table.number

        db.session.commit()

        old_table_name = old_table.display_name if old_table else "Aucune table"
        new_table_name = new_table.display_name

        return jsonify({
            'success': True,
            'message': f'Commande déplacée de {old_table_name} vers {new_table_name}',
            'old_table': old_table_name,
            'new_table': new_table_name
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/send_order_updates_to_kitchen', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def send_order_updates_to_kitchen():
    """API pour envoyer les modifications d'une commande en cuisine"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        kitchen_note = data.get('kitchen_note', '')

        if not sale_id:
            return jsonify({'success': False, 'error': 'ID de commande manquant'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Mettre à jour la note cuisine
        if kitchen_note:
            sale.kitchen_note = kitchen_note

        # Si la commande était marquée comme prête, la remettre en préparation
        if sale.kitchen_status == 'ready':
            sale.kitchen_status = 'modified'
            sale.status = SaleStatus.KITCHEN_PENDING
        else:
            # Marquer comme modifié en cuisine
            sale.kitchen_status = 'modified'

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Modifications envoyées en cuisine',
            'kitchen_status': sale.kitchen_status
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/cancel_order', methods=['POST'])
@login_required
@permission_required('can_process_sales')
def cancel_order():
    """API pour annuler une commande"""
    try:
        data = request.get_json()
        sale_id = data.get('sale_id')
        reason = data.get('reason', 'Annulée par l\'utilisateur')

        if not sale_id:
            return jsonify({'success': False, 'error': 'ID de commande manquant'})

        sale = Sale.query.get_or_404(sale_id)
        if sale.owner_id != current_user.get_owner_id():
            return jsonify({'success': False, 'error': 'Accès non autorisé'}), 403

        # Libérer la table
        if sale.table_id:
            table = Table.query.get(sale.table_id)
            if table:
                table.reset_table(commit=False)

        # Marquer la vente comme annulée
        sale.status = SaleStatus.CANCELLED
        sale.kitchen_note = f"ANNULÉE: {reason}"

        db.session.commit()

        return jsonify({
            'success': True,
            'message': 'Commande annulée avec succès',
            'reason': reason
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@bp.route('/api/get_products_and_categories')
@login_required
@permission_required('can_process_sales')
def get_products_and_categories():
    """API pour récupérer les produits et catégories pour l'ajout d'articles"""
    try:
        owner_id = current_user.get_owner_id()

        # Récupérer les catégories
        categories = ProductCategory.query.filter_by(owner_id=owner_id).order_by(ProductCategory.name).all()
        categories_data = []
        for category in categories:
            categories_data.append({
                'id': category.id,
                'name': category.name,
                'description': category.description
            })

        # Récupérer les produits actifs
        products = Product.query.filter_by(owner_id=owner_id, is_active=True).order_by(Product.name).all()
        products_data = []
        for product in products:
            products_data.append({
                'id': product.id,
                'name': product.name,
                'description': product.description,
                'price': float(product.price),
                'category_id': product.category_id,
                'stock_quantity': product.stock_quantity,
                'is_active': product.is_active
            })

        return jsonify({
            'success': True,
            'categories': categories_data,
            'products': products_data
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500