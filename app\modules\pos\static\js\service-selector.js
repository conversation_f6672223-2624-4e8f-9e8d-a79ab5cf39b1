/**
 * Gestionnaire de sélection de service et de tables
 */
class ServiceSelector {
    constructor() {
        this.selectedService = null;
        this.selectedRoom = null;
        this.selectedTable = null;
        this.selectedCovers = 1;
        this.rooms = [];
        this.currentRoomTables = [];
        
        this.initializeEventListeners();
        this.loadRooms();
    }

    initializeEventListeners() {
        // Sélection du type de service
        document.addEventListener('click', (e) => {
            if (e.target.closest('.service-option')) {
                this.selectService(e.target.closest('.service-option'));
            }
        });

        // Sélection de salle
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-room-id]')) {
                this.selectRoom(e.target.closest('[data-room-id]').dataset.roomId);
            }
        });

        // Sélection de table
        document.addEventListener('click', (e) => {
            if (e.target.closest('.table-mini')) {
                this.selectTable(e.target.closest('.table-mini'));
            }
        });

        // Sélection du nombre de couverts
        document.addEventListener('click', (e) => {
            if (e.target.closest('[data-covers]')) {
                this.selectCovers(e.target.closest('[data-covers]').dataset.covers);
            }
        });

        // Confirmation des couverts
        document.getElementById('confirmCovers')?.addEventListener('click', () => {
            this.confirmCovers();
        });

        // Input personnalisé pour les couverts
        document.getElementById('customCovers')?.addEventListener('input', (e) => {
            this.selectCovers(e.target.value);
        });
    }

    async loadRooms() {
        try {
            const response = await fetch('/pos/api/get_rooms');
            if (response.ok) {
                this.rooms = await response.json();
            }
        } catch (error) {
            console.error('Erreur lors du chargement des salles:', error);
        }
    }

    selectService(serviceElement) {
        // Désélectionner les autres options
        document.querySelectorAll('.service-card').forEach(card => {
            card.classList.remove('selected');
        });

        // Sélectionner la nouvelle option
        serviceElement.querySelector('.service-card').classList.add('selected');
        this.selectedService = serviceElement.dataset.service;

        // Si c'est "sur place", ouvrir la sélection de table
        if (this.selectedService === 'dine_in') {
            setTimeout(() => {
                this.openRoomTableModal();
            }, 500);
        } else {
            // Pour les autres services, aller directement aux couverts
            setTimeout(() => {
                this.openCoversModal();
            }, 500);
        }
    }

    async openRoomTableModal() {
        const modal = new bootstrap.Modal(document.getElementById('roomTableModal'));
        
        // Charger les salles
        await this.loadRoomSelector();
        
        // Sélectionner la salle par défaut
        const defaultRoom = this.rooms.find(room => room.is_default) || this.rooms[0];
        if (defaultRoom) {
            await this.selectRoom(defaultRoom.id);
        }
        
        modal.show();
    }

    async loadRoomSelector() {
        const roomSelector = document.getElementById('roomSelector');
        roomSelector.innerHTML = '';

        this.rooms.forEach(room => {
            const button = document.createElement('button');
            button.type = 'button';
            button.className = 'btn btn-outline-primary';
            button.dataset.roomId = room.id;
            button.innerHTML = `${room.name} <span class="badge bg-secondary">${room.table_count}</span>`;
            roomSelector.appendChild(button);
        });
    }

    async selectRoom(roomId) {
        // Mettre à jour l'interface
        document.querySelectorAll('[data-room-id]').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`[data-room-id="${roomId}"]`)?.classList.add('active');

        this.selectedRoom = roomId;

        // Charger les tables de la salle
        await this.loadRoomTables(roomId);
    }

    async loadRoomTables(roomId) {
        try {
            const response = await fetch(`/rooms/api/get_room_data/${roomId}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.currentRoomTables = data.tables;
                    this.renderRoomPlan(data.room, data.tables);
                } else {
                    console.error('Erreur API:', data.message);
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des tables:', error);
        }
    }

    renderRoomPlan(room, tables) {
        const roomPlan = document.getElementById('roomPlan');
        roomPlan.innerHTML = '';

        // Créer le conteneur du plan
        const planContainer = document.createElement('div');
        planContainer.style.position = 'relative';
        planContainer.style.width = Math.min(room.width, 800) + 'px';
        planContainer.style.height = Math.min(room.height, 400) + 'px';
        planContainer.style.backgroundColor = room.background_color;
        planContainer.style.border = '2px solid #dee2e6';
        planContainer.style.margin = '0 auto';
        planContainer.style.overflow = 'hidden';

        // Ajouter les tables
        tables.forEach(table => {
            const tableElement = document.createElement('div');
            tableElement.className = `table-mini ${table.status}`;
            tableElement.dataset.tableId = table.id;
            tableElement.style.position = 'absolute';
            tableElement.style.left = (table.position_x * Math.min(room.width, 800) / room.width) + 'px';
            tableElement.style.top = (table.position_y * Math.min(room.height, 400) / room.height) + 'px';
            tableElement.textContent = table.number;
            
            // Ajouter les informations de la table
            if (table.current_covers > 0) {
                tableElement.title = `Table ${table.number} - ${table.current_covers} couverts - ${table.current_amount}€`;
            } else {
                tableElement.title = `Table ${table.number} - Libre`;
            }

            // Désactiver les tables occupées
            if (table.status !== 'available') {
                tableElement.style.opacity = '0.5';
                tableElement.style.cursor = 'not-allowed';
            }

            planContainer.appendChild(tableElement);
        });

        roomPlan.appendChild(planContainer);
    }

    selectTable(tableElement) {
        const tableId = tableElement.dataset.tableId;
        const table = this.currentRoomTables.find(t => t.id == tableId);

        // Si la table est occupée, afficher la commande en cours
        if (table.status === 'occupied') {
            this.showCurrentOrder(table);
            return;
        }

        // Vérifier si la table est disponible
        if (table.status !== 'available') {
            alert('Cette table n\'est pas disponible');
            return;
        }

        // Désélectionner les autres tables
        document.querySelectorAll('.table-mini').forEach(t => {
            t.classList.remove('selected');
        });

        // Sélectionner la nouvelle table
        tableElement.classList.add('selected');
        this.selectedTable = table;

        // Fermer le modal de sélection de table et ouvrir celui des couverts
        bootstrap.Modal.getInstance(document.getElementById('roomTableModal')).hide();
        setTimeout(() => {
            this.openCoversModal();
        }, 300);
    }

    openCoversModal() {
        const modal = new bootstrap.Modal(document.getElementById('coversModal'));
        
        // Réinitialiser la sélection
        document.querySelectorAll('.cover-option').forEach(option => {
            option.classList.remove('selected');
        });
        document.getElementById('customCovers').value = '';
        
        modal.show();
    }

    selectCovers(covers) {
        this.selectedCovers = parseInt(covers);

        // Mettre à jour l'interface
        document.querySelectorAll('.cover-option').forEach(option => {
            option.classList.remove('selected');
        });

        const selectedOption = document.querySelector(`[data-covers="${covers}"]`);
        if (selectedOption) {
            selectedOption.classList.add('selected');
        }

        // Vider l'input personnalisé si on sélectionne un bouton
        if (selectedOption) {
            document.getElementById('customCovers').value = '';
        }
    }

    confirmCovers() {
        // Vérifier l'input personnalisé
        const customCovers = document.getElementById('customCovers').value;
        if (customCovers) {
            this.selectedCovers = parseInt(customCovers);
        }

        if (this.selectedCovers < 1) {
            alert('Veuillez sélectionner un nombre de couverts valide');
            return;
        }

        // Fermer le modal et procéder à la commande
        bootstrap.Modal.getInstance(document.getElementById('coversModal')).hide();
        
        // Fermer aussi le modal de service
        const serviceModal = bootstrap.Modal.getInstance(document.getElementById('serviceModal'));
        if (serviceModal) {
            serviceModal.hide();
        }

        // Procéder à la création de la commande
        this.proceedToOrder();
    }

    proceedToOrder() {
        // Préparer les données de la commande
        const orderData = {
            service_type: this.selectedService,
            covers_count: this.selectedCovers,
            table_id: this.selectedTable?.id || null,
            room_id: this.selectedRoom || null
        };

        // Stocker les données dans le sessionStorage pour la page de commande
        sessionStorage.setItem('newOrderData', JSON.stringify(orderData));

        // Afficher un message de confirmation
        const serviceName = this.getServiceName(this.selectedService);
        let message = `Nouvelle commande: ${serviceName}`;
        
        if (this.selectedTable) {
            message += ` - Table ${this.selectedTable.number}`;
        }
        message += ` - ${this.selectedCovers} couvert(s)`;

        // Afficher dans l'interface POS
        this.displayOrderInfo(orderData);

        // Réinitialiser la sélection
        this.resetSelection();
    }

    getServiceName(serviceType) {
        const serviceNames = {
            'dine_in': 'Sur place',
            'takeaway': 'À emporter',
            'delivery': 'Livraison',
            'drive_thru': 'Service au volant'
        };
        return serviceNames[serviceType] || serviceType;
    }

    displayOrderInfo(orderData) {
        // Mettre à jour l'interface POS avec les informations de commande
        const orderInfoElement = document.getElementById('current-order-info');
        if (orderInfoElement) {
            let info = `<strong>${this.getServiceName(orderData.service_type)}</strong>`;
            
            if (orderData.table_id && this.selectedTable) {
                info += ` - Table ${this.selectedTable.number}`;
            }
            info += ` - ${orderData.covers_count} couvert(s)`;
            
            orderInfoElement.innerHTML = info;
        }

        // Activer le panier si ce n'est pas déjà fait
        if (typeof POS !== 'undefined') {
            POS.setOrderData(orderData);
        }
    }

    resetSelection() {
        this.selectedService = null;
        this.selectedRoom = null;
        this.selectedTable = null;
        this.selectedCovers = 1;
    }

    // Gestion des commandes en cours
    async showCurrentOrder(table) {
        try {
            // Fermer le modal de sélection de table
            const roomTableModal = bootstrap.Modal.getInstance(document.getElementById('roomTableModal'));
            if (roomTableModal) {
                roomTableModal.hide();
            }

            // Charger les données de la commande
            const response = await fetch(`/pos/api/get_table_order/${table.id}`);
            const data = await response.json();

            if (!data.success) {
                alert(data.error || 'Erreur lors du chargement de la commande');
                return;
            }

            this.currentOrder = data.order;
            this.displayCurrentOrder();

            // Ouvrir le modal de commande en cours
            const currentOrderModal = new bootstrap.Modal(document.getElementById('currentOrderModal'));
            currentOrderModal.show();

        } catch (error) {
            console.error('Erreur lors du chargement de la commande:', error);
            alert('Erreur lors du chargement de la commande');
        }
    }

    displayCurrentOrder() {
        const order = this.currentOrder;

        // Titre avec nom de la table
        document.getElementById('currentOrderTableName').textContent = order.table_name;

        // Informations de la commande
        const orderInfo = document.getElementById('currentOrderInfo');
        orderInfo.innerHTML = `
            <div class="mb-2">
                <strong>Commande #${order.id}</strong>
            </div>
            <div class="mb-2">
                <small class="text-muted">Service: ${order.service_type_display}</small>
            </div>
            <div class="mb-2">
                <small class="text-muted">Statut: ${this.getStatusDisplay(order.status)}</small>
            </div>
            <div class="mb-2">
                <small class="text-muted">Cuisine: ${this.getKitchenStatusDisplay(order.kitchen_status)}</small>
            </div>
            <div class="mb-2">
                <strong>Total: ${order.total.toFixed(2)} €</strong>
            </div>
            ${order.total_paid > 0 ? `
                <div class="mb-2">
                    <small class="text-success">Payé: ${order.total_paid.toFixed(2)} €</small>
                </div>
                <div class="mb-2">
                    <small class="text-warning">Restant: ${order.remaining_amount.toFixed(2)} €</small>
                </div>
            ` : ''}
        `;

        // Nombre de couverts
        document.getElementById('currentCoversCount').value = order.covers_count;

        // Articles de la commande
        this.displayOrderItems(order.items);

        // Note cuisine
        document.getElementById('currentOrderNote').value = order.kitchen_note || '';

        // Initialiser les événements
        this.initCurrentOrderEvents();
    }

    displayOrderItems(items) {
        const tbody = document.getElementById('currentOrderItems');
        tbody.innerHTML = '';

        let total = 0;
        items.forEach(item => {
            total += item.total_price;

            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.product_name}</td>
                <td>${item.unit_price.toFixed(2)} €</td>
                <td>
                    <div class="input-group input-group-sm">
                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="window.serviceSelector.updateItemQuantity(${item.id}, ${item.quantity - 1})">
                            <i class="fas fa-minus"></i>
                        </button>
                        <input type="number" class="form-control text-center" value="${item.quantity}" min="0"
                               onchange="window.serviceSelector.updateItemQuantity(${item.id}, this.value)">
                        <button class="btn btn-outline-secondary btn-sm" type="button" onclick="window.serviceSelector.updateItemQuantity(${item.id}, ${item.quantity + 1})">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                </td>
                <td>${item.total_price.toFixed(2)} €</td>
                <td>
                    <button class="btn btn-danger btn-sm" onclick="window.serviceSelector.removeItem(${item.id})" title="Supprimer">
                        <i class="fas fa-trash"></i>
                    </button>
                </td>
            `;
            tbody.appendChild(row);
        });

        document.getElementById('currentOrderTotal').textContent = total.toFixed(2) + ' €';
    }

    initCurrentOrderEvents() {
        // Gestion des couverts
        document.getElementById('decreaseCovers').onclick = () => {
            const input = document.getElementById('currentCoversCount');
            const newValue = Math.max(1, parseInt(input.value) - 1);
            input.value = newValue;
            this.updateOrderCovers(newValue);
        };

        document.getElementById('increaseCovers').onclick = () => {
            const input = document.getElementById('currentCoversCount');
            const newValue = parseInt(input.value) + 1;
            input.value = newValue;
            this.updateOrderCovers(newValue);
        };

        // Actions
        document.getElementById('moveTableBtn').onclick = () => this.openMoveTableModal();
        document.getElementById('addItemsBtn').onclick = () => this.openAddItemsModal();
        document.getElementById('addItemsModalBtn').onclick = () => this.openAddItemsModal();
        document.getElementById('editInPosBtn').onclick = () => this.editOrderInPos();
        document.getElementById('updateOrderBtn').onclick = () => this.updateOrder();
        document.getElementById('sendUpdatesToKitchenBtn').onclick = () => this.sendUpdatesToKitchen();
        document.getElementById('cancelOrderBtn').onclick = () => this.cancelOrder();
        document.getElementById('printOrderBtn').onclick = () => this.printOrder();
    }

    async updateItemQuantity(itemId, newQuantity) {
        try {
            const response = await fetch('/pos/api/update_order_item', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                },
                body: JSON.stringify({
                    item_id: itemId,
                    quantity: parseInt(newQuantity)
                })
            });

            const data = await response.json();
            if (data.success) {
                // Recharger la commande
                await this.showCurrentOrder(this.currentOrder);
                this.showAlert('success', data.message);
            } else {
                this.showAlert('error', data.error);
            }
        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);
            this.showAlert('error', 'Erreur lors de la mise à jour');
        }
    }

    async removeItem(itemId) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cet article ?')) {
            await this.updateItemQuantity(itemId, 0);
        }
    }

    async updateOrderCovers(newCovers) {
        try {
            const response = await fetch('/pos/api/update_order_covers', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                },
                body: JSON.stringify({
                    sale_id: this.currentOrder.id,
                    covers_count: newCovers
                })
            });

            const data = await response.json();
            if (data.success) {
                this.currentOrder.covers_count = newCovers;
                this.showAlert('success', data.message);
            } else {
                this.showAlert('error', data.error);
            }
        } catch (error) {
            console.error('Erreur lors de la mise à jour des couverts:', error);
            this.showAlert('error', 'Erreur lors de la mise à jour des couverts');
        }
    }

    async openMoveTableModal() {
        try {
            // Charger les salles
            await this.loadRooms();

            // Préparer le sélecteur de salle pour déplacement
            const moveRoomSelector = document.getElementById('moveRoomSelector');
            moveRoomSelector.innerHTML = '';

            this.rooms.forEach(room => {
                const button = document.createElement('button');
                button.type = 'button';
                button.className = 'btn btn-outline-primary';
                button.dataset.roomId = room.id;
                button.innerHTML = `${room.name} <span class="badge bg-secondary">${room.available_tables_count}</span>`;
                button.onclick = () => this.selectMoveRoom(room.id);
                moveRoomSelector.appendChild(button);
            });

            // Sélectionner la salle par défaut
            const defaultRoom = this.rooms.find(room => room.is_default) || this.rooms[0];
            if (defaultRoom) {
                await this.selectMoveRoom(defaultRoom.id);
            }

            // Ouvrir le modal
            const moveTableModal = new bootstrap.Modal(document.getElementById('moveTableModal'));
            moveTableModal.show();

        } catch (error) {
            console.error('Erreur lors de l\'ouverture du modal de déplacement:', error);
            this.showAlert('error', 'Erreur lors de l\'ouverture du modal de déplacement');
        }
    }

    async selectMoveRoom(roomId) {
        // Mettre à jour l'interface
        document.querySelectorAll('#moveRoomSelector button').forEach(btn => {
            btn.classList.remove('active');
        });
        document.querySelector(`#moveRoomSelector [data-room-id="${roomId}"]`)?.classList.add('active');

        // Charger les tables de la salle
        await this.loadMoveRoomTables(roomId);
    }

    async loadMoveRoomTables(roomId) {
        try {
            const response = await fetch(`/rooms/api/get_room_data/${roomId}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success) {
                    this.renderMoveRoomPlan(data.room, data.tables);
                }
            }
        } catch (error) {
            console.error('Erreur lors du chargement des tables pour déplacement:', error);
        }
    }

    renderMoveRoomPlan(room, tables) {
        const moveRoomPlan = document.getElementById('moveRoomPlan');
        moveRoomPlan.innerHTML = '';

        // Créer le conteneur du plan
        const planContainer = document.createElement('div');
        planContainer.style.position = 'relative';
        planContainer.style.width = Math.min(room.width, 600) + 'px';
        planContainer.style.height = Math.min(room.height, 300) + 'px';
        planContainer.style.backgroundColor = room.background_color;
        planContainer.style.border = '2px solid #dee2e6';
        planContainer.style.margin = '0 auto';
        planContainer.style.overflow = 'hidden';

        // Ajouter les tables
        tables.forEach(table => {
            // Ne pas afficher la table actuelle
            if (table.id === this.currentOrder.table_id) {
                return;
            }

            const tableElement = document.createElement('div');
            tableElement.className = `table-mini ${table.status}`;
            tableElement.dataset.tableId = table.id;
            tableElement.style.position = 'absolute';
            tableElement.style.left = (table.position_x * Math.min(room.width, 600) / room.width) + 'px';
            tableElement.style.top = (table.position_y * Math.min(room.height, 300) / room.height) + 'px';
            tableElement.textContent = table.number;
            tableElement.title = `Table ${table.number} - ${table.status}`;

            // Seules les tables disponibles sont cliquables
            if (table.status === 'available') {
                tableElement.onclick = () => this.selectMoveTable(table);
            } else {
                tableElement.style.opacity = '0.5';
                tableElement.style.cursor = 'not-allowed';
            }

            planContainer.appendChild(tableElement);
        });

        moveRoomPlan.appendChild(planContainer);
    }

    selectMoveTable(table) {
        // Désélectionner les autres tables
        document.querySelectorAll('#moveRoomPlan .table-mini').forEach(t => {
            t.classList.remove('selected');
        });

        // Sélectionner la nouvelle table
        const tableElement = document.querySelector(`#moveRoomPlan [data-table-id="${table.id}"]`);
        if (tableElement) {
            tableElement.classList.add('selected');
        }

        this.selectedMoveTable = table;

        // Activer le bouton de confirmation
        document.getElementById('confirmMoveBtn').disabled = false;
        document.getElementById('confirmMoveBtn').onclick = () => this.confirmMoveTable();
    }

    async confirmMoveTable() {
        if (!this.selectedMoveTable) {
            this.showAlert('error', 'Veuillez sélectionner une table de destination');
            return;
        }

        try {
            const response = await fetch('/pos/api/move_order_to_table', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                },
                body: JSON.stringify({
                    sale_id: this.currentOrder.id,
                    new_table_id: this.selectedMoveTable.id
                })
            });

            const data = await response.json();
            if (data.success) {
                // Fermer les modals
                bootstrap.Modal.getInstance(document.getElementById('moveTableModal')).hide();
                bootstrap.Modal.getInstance(document.getElementById('currentOrderModal')).hide();

                this.showAlert('success', data.message);

                // Recharger la page ou mettre à jour l'interface
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                this.showAlert('error', data.error);
            }
        } catch (error) {
            console.error('Erreur lors du déplacement:', error);
            this.showAlert('error', 'Erreur lors du déplacement de la commande');
        }
    }

    async openAddItemsModal() {
        try {
            // Charger les catégories et produits
            await this.loadProductsForAddition();

            // Ouvrir le modal
            const addItemsModal = new bootstrap.Modal(document.getElementById('addItemsModal'));
            addItemsModal.show();

        } catch (error) {
            console.error('Erreur lors de l\'ouverture du modal d\'ajout:', error);
            this.showAlert('error', 'Erreur lors de l\'ouverture du modal d\'ajout');
        }
    }

    editOrderInPos() {
        // Fermer la modale actuelle
        bootstrap.Modal.getInstance(document.getElementById('currentOrderModal')).hide();

        // Rediriger vers la page POS avec l'ID de la commande
        window.location.href = `/pos/?edit_order=${this.currentOrder.id}`;
    }

    async loadProductsForAddition() {
        try {
            console.log('Chargement des produits et catégories...');
            const response = await fetch('/pos/api/get_products_and_categories');

            if (response.ok) {
                const data = await response.json();
                console.log('Données reçues:', data);

                if (data.success) {
                    if (data.categories && data.products) {
                        console.log(`${data.categories.length} catégories et ${data.products.length} produits trouvés`);
                        this.renderProductsForAddition(data.categories, data.products);
                    } else {
                        console.error('Données manquantes dans la réponse');
                        this.showAlert('error', 'Aucune donnée de produits trouvée');
                    }
                } else {
                    console.error('Erreur API:', data.error);
                    this.showAlert('error', data.error || 'Erreur lors du chargement des produits');
                }
            } else {
                console.error('Erreur HTTP:', response.status);
                this.showAlert('error', `Erreur ${response.status}: Impossible de charger les produits`);
            }
        } catch (error) {
            console.error('Erreur lors du chargement des produits:', error);
            this.showAlert('error', 'Erreur de connexion lors du chargement des produits');
        }
    }

    renderProductsForAddition(categories, products) {
        console.log('Rendu des produits:', categories, products);

        // Rendu des catégories
        const categoriesContainer = document.getElementById('addItemsCategories');
        if (!categoriesContainer) {
            console.error('Container des catégories non trouvé');
            return;
        }

        categoriesContainer.innerHTML = '';

        if (!categories || categories.length === 0) {
            categoriesContainer.innerHTML = '<div class="text-muted p-3">Aucune catégorie disponible</div>';
            return;
        }

        categories.forEach((category, index) => {
            const categoryElement = document.createElement('a');
            categoryElement.href = '#';
            categoryElement.className = `list-group-item list-group-item-action ${index === 0 ? 'active' : ''}`;
            categoryElement.textContent = category.name;
            categoryElement.onclick = (e) => {
                e.preventDefault();
                this.selectCategoryForAddition(category.id, categoryElement);
            };
            categoriesContainer.appendChild(categoryElement);
        });

        // Afficher les produits de la première catégorie
        if (categories.length > 0) {
            this.selectCategoryForAddition(categories[0].id);
        }

        this.allProducts = products || [];
        this.tempCart = [];
        this.updateTempCartDisplay();
    }

    selectCategoryForAddition(categoryId, categoryElement = null) {
        // Mettre à jour l'interface des catégories
        if (categoryElement) {
            document.querySelectorAll('#addItemsCategories .list-group-item').forEach(item => {
                item.classList.remove('active');
            });
            categoryElement.classList.add('active');
        }

        // Filtrer et afficher les produits
        const categoryProducts = this.allProducts.filter(p => p.category_id === categoryId);
        this.renderProductsForCategory(categoryProducts);
    }

    renderProductsForCategory(products) {
        const productsContainer = document.getElementById('addItemsProducts');
        productsContainer.innerHTML = '';

        products.forEach(product => {
            const productElement = document.createElement('div');
            productElement.className = 'col-md-6 mb-2';
            productElement.innerHTML = `
                <div class="card product-card h-100" onclick="window.serviceSelector.addToTempCart(${product.id})">
                    <div class="card-body p-2">
                        <h6 class="card-title mb-1">${product.name}</h6>
                        <p class="card-text small text-muted mb-1">${product.description || ''}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <strong class="text-primary">${product.price.toFixed(2)} €</strong>
                            <small class="text-muted">Stock: ${product.stock_quantity || 0}</small>
                        </div>
                    </div>
                </div>
            `;
            productsContainer.appendChild(productElement);
        });
    }

    addToTempCart(productId) {
        const product = this.allProducts.find(p => p.id === productId);
        if (!product) return;

        // Vérifier si le produit est déjà dans le panier temporaire
        const existingItem = this.tempCart.find(item => item.product_id === productId);

        if (existingItem) {
            existingItem.quantity += 1;
        } else {
            this.tempCart.push({
                product_id: productId,
                product_name: product.name,
                unit_price: product.price,
                quantity: 1
            });
        }

        this.updateTempCartDisplay();
    }

    updateTempCartDisplay() {
        const tempCartItems = document.getElementById('tempCartItems');
        const tempCartTotal = document.getElementById('tempCartTotal');

        if (this.tempCart.length === 0) {
            tempCartItems.innerHTML = '<p class="text-muted text-center">Aucun article sélectionné</p>';
            tempCartTotal.textContent = '0.00 €';
            document.getElementById('confirmAddItemsBtn').disabled = true;
            return;
        }

        let total = 0;
        tempCartItems.innerHTML = '';

        this.tempCart.forEach((item, index) => {
            const itemTotal = item.unit_price * item.quantity;
            total += itemTotal;

            const itemElement = document.createElement('div');
            itemElement.className = 'mb-2 p-2 border rounded';
            itemElement.innerHTML = `
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <strong>${item.product_name}</strong><br>
                        <small>${item.unit_price.toFixed(2)} € x ${item.quantity}</small>
                    </div>
                    <div class="text-end">
                        <div>${itemTotal.toFixed(2)} €</div>
                        <button class="btn btn-sm btn-outline-danger" onclick="window.serviceSelector.removeFromTempCart(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `;
            tempCartItems.appendChild(itemElement);
        });

        tempCartTotal.textContent = total.toFixed(2) + ' €';
        document.getElementById('confirmAddItemsBtn').disabled = false;
        document.getElementById('confirmAddItemsBtn').onclick = () => this.confirmAddItems();
    }

    removeFromTempCart(index) {
        this.tempCart.splice(index, 1);
        this.updateTempCartDisplay();
    }

    async confirmAddItems() {
        if (this.tempCart.length === 0) {
            this.showAlert('error', 'Aucun article à ajouter');
            return;
        }

        try {
            const response = await fetch('/pos/api/add_items_to_order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                },
                body: JSON.stringify({
                    sale_id: this.currentOrder.id,
                    items: this.tempCart,
                    kitchen_note: document.getElementById('currentOrderNote').value
                })
            });

            const data = await response.json();
            if (data.success) {
                // Fermer le modal d'ajout
                bootstrap.Modal.getInstance(document.getElementById('addItemsModal')).hide();

                // Recharger la commande
                await this.showCurrentOrder(this.currentOrder);

                this.showAlert('success', data.message);
            } else {
                this.showAlert('error', data.error);
            }
        } catch (error) {
            console.error('Erreur lors de l\'ajout des articles:', error);
            this.showAlert('error', 'Erreur lors de l\'ajout des articles');
        }
    }

    async updateOrder() {
        try {
            // Mettre à jour la note cuisine
            const kitchenNote = document.getElementById('currentOrderNote').value;

            const response = await fetch('/pos/api/send_order_updates_to_kitchen', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                },
                body: JSON.stringify({
                    sale_id: this.currentOrder.id,
                    kitchen_note: kitchenNote
                })
            });

            const data = await response.json();
            if (data.success) {
                this.showAlert('success', 'Commande mise à jour avec succès');
            } else {
                this.showAlert('error', data.error);
            }
        } catch (error) {
            console.error('Erreur lors de la mise à jour:', error);
            this.showAlert('error', 'Erreur lors de la mise à jour de la commande');
        }
    }

    async sendUpdatesToKitchen() {
        if (confirm('Envoyer les modifications en cuisine ?')) {
            await this.updateOrder();
        }
    }

    async cancelOrder() {
        const reason = prompt('Raison de l\'annulation (optionnel):');
        if (reason !== null) { // null si l'utilisateur a cliqué sur Annuler
            try {
                const response = await fetch('/pos/api/cancel_order', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name=csrf-token]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        sale_id: this.currentOrder.id,
                        reason: reason || 'Annulée par l\'utilisateur'
                    })
                });

                const data = await response.json();
                if (data.success) {
                    // Fermer le modal
                    bootstrap.Modal.getInstance(document.getElementById('currentOrderModal')).hide();

                    this.showAlert('success', data.message);

                    // Recharger la page
                    setTimeout(() => {
                        location.reload();
                    }, 1500);
                } else {
                    this.showAlert('error', data.error);
                }
            } catch (error) {
                console.error('Erreur lors de l\'annulation:', error);
                this.showAlert('error', 'Erreur lors de l\'annulation de la commande');
            }
        }
    }

    printOrder() {
        // Ouvrir la page d'impression de la commande
        window.open(`/pos/sales/${this.currentOrder.id}/print`, '_blank');
    }

    // Méthodes utilitaires
    getStatusDisplay(status) {
        const statusMap = {
            'pending': 'En attente',
            'kitchen_pending': 'En cuisine',
            'ready': 'Prête',
            'paid': 'Payée',
            'completed': 'Terminée',
            'cancelled': 'Annulée'
        };
        return statusMap[status] || status;
    }

    getKitchenStatusDisplay(kitchenStatus) {
        const statusMap = {
            'pending': 'En attente',
            'preparing': 'En préparation',
            'ready': 'Prête',
            'served': 'Servie',
            'modified': 'Modifiée'
        };
        return statusMap[kitchenStatus] || kitchenStatus;
    }

    showAlert(type, message) {
        // Créer une alerte Bootstrap
        const alertContainer = document.getElementById('alert-container') || document.body;
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        alertElement.style.top = '20px';
        alertElement.style.right = '20px';
        alertElement.style.zIndex = '9999';
        alertElement.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        alertContainer.appendChild(alertElement);

        // Supprimer automatiquement après 5 secondes
        setTimeout(() => {
            if (alertElement.parentNode) {
                alertElement.parentNode.removeChild(alertElement);
            }
        }, 5000);
    }

    // Méthode publique pour ouvrir le sélecteur de service
    static openServiceSelector() {
        const modal = new bootstrap.Modal(document.getElementById('serviceModal'));
        modal.show();
    }
}

// Initialiser le sélecteur de service
document.addEventListener('DOMContentLoaded', function() {
    window.serviceSelector = new ServiceSelector();
});

// Fonction globale pour ouvrir le sélecteur
window.openServiceSelector = function() {
    ServiceSelector.openServiceSelector();
};
