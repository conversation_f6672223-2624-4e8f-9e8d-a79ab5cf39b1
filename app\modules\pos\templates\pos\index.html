{% extends "base.html" %}

{% block title %}Point de Vente{% endblock %}

{% block head_scripts %}
<script src="{{ url_for('pos.static', filename='js/pos.js') }}"></script>
<script src="{{ url_for('pos.static', filename='js/main.js') }}"></script>
<script src="{{ url_for('pos.static', filename='js/cart-ui.js') }}"></script>
<script src="{{ url_for('pos.static', filename='js/service-selector.js') }}"></script>
{% endblock %}

{% block extra_css %}
<style>
/* Styles de base et variables */
:root {
    --header-height: 60px;
    --sidebar-width: 280px;
    --cart-width: 320px;
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --light-color: #f8f9fa;
    --border-color: #dee2e6;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
    --radius: 8px;
}

/* Barre de navigation POS */
.pos-navbar {
    background-color: white;
    border-bottom: 2px solid var(--border-color);
    box-shadow: var(--shadow);
    margin-bottom: 10px;
}

.pos-nav-buttons .btn {
    font-size: 13px;
    padding: 6px 12px;
}

/* Layout responsive */
.main-content {
    display: flex;
    height: calc(100vh - var(--header-height));
    gap: 10px;
    padding: 10px;
    position: relative;
    max-width: 100%;
    margin: 0;
}

@media (max-width: 1200px) {
    .main-content {
        flex-direction: column;
        height: auto;
    }

    .left-panel, .right-panel {
        width: 100%;
    }

    .cart-container {
        height: 400px;
    }
}

/* Panneau gauche */
.left-panel {
    width: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Panneau central */
.center-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    background-color: var(--light-color);
    border-radius: var(--radius);
    padding: 10px;
}

/* Panneau droit */
.right-panel {
    width: var(--cart-width);
}

/* Barre de recherche */
.search-bar {
    margin-bottom: 10px;
}

.search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    font-size: 14px;
}

/* Catégories */
.category-tabs {
    display: flex;
    overflow-x: auto;
    gap: 5px;
    padding: 5px 0;
    margin-bottom: 10px;
    scrollbar-width: thin;
    -webkit-overflow-scrolling: touch;
}

.category-tab {
    padding: 6px 12px;
    border-radius: 15px;
    background-color: var(--light-color);
    white-space: nowrap;
    cursor: pointer;
    transition: all 0.2s;
    border: 1px solid var(--border-color);
    font-size: 14px;
}

.category-tab.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Grille de produits */
.products-container {
    flex: 1;
    overflow-y: auto;
    background-color: white;
    border-radius: var(--radius);
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
    padding: 8px;
}

.product-btn {
    height: 100px;
    border-radius: var(--radius);
    border: 1px solid var(--border-color);
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px;
    transition: all 0.2s;
    text-align: center;
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.product-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.product-btn .image-container {
    width: 50px;
    height: 50px;
    margin-bottom: 5px;
    border-radius: var(--radius);
    overflow: hidden;
}

.product-btn img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-btn .product-name {
    font-weight: bold;
    font-size: 12px;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 2px;
}

.product-btn .price {
    font-size: 14px;
    color: var(--success-color);
    font-weight: bold;
}

.product-btn .stock {
    font-size: 10px;
    color: var(--secondary-color);
    position: absolute;
    bottom: 2px;
    right: 2px;
    background-color: var(--light-color);
    padding: 1px 4px;
    border-radius: 8px;
}

.stock-warning {
    color: var(--danger-color);
}

.stock-updated {
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0% {
        background-color: transparent;
    }
    50% {
        background-color: rgba(255, 193, 7, 0.3);
    }
    100% {
        background-color: transparent;
    }
}

/* Informations de commande en cours */
.current-order-info {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 8px rgba(0,123,255,0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.current-order-info .order-details {
    display: flex;
    align-items: center;
    gap: 15px;
}

.current-order-info .order-actions {
    display: flex;
    gap: 10px;
}

.current-order-info .btn {
    padding: 4px 8px;
    font-size: 12px;
}
</style>
{% endblock %}

{% block content %}
<!-- Barre de navigation POS -->
<div class="pos-navbar">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center py-2">
            <h4 class="mb-0">
                <i class="fas fa-cash-register"></i> Point de Vente
            </h4>
            <div class="pos-nav-buttons">
                <button class="btn btn-success btn-sm me-2" onclick="openServiceSelector()">
                    <i class="fas fa-plus"></i> Nouvelle commande
                </button>
                <button class="btn btn-outline-info btn-sm me-2" onclick="openRoomSelector()">
                    <i class="fas fa-map"></i> Salles
                </button>
                <a href="{{ url_for('pos.sales') }}" class="btn btn-outline-primary btn-sm me-2">
                    <i class="fas fa-list"></i> Ventes
                </a>
                <a href="{{ url_for('pos.kitchen_orders') }}" class="btn btn-outline-warning btn-sm me-2">
                    <i class="fas fa-utensils"></i> Cuisine
                </a>
                <a href="{{ url_for('pos.ready_orders') }}" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-bell"></i> Commandes prêtes
                </a>
            </div>
        </div>
    </div>
</div>

<div class="main-content">
    <!-- Panneau gauche -->
    <div class="left-panel">
        <!-- Pavé numérique et autres contrôles -->
        {% include 'pos/_numpad.html' %}
    </div>

    <!-- Panneau central -->
    <div class="center-panel">
        <!-- Informations de commande en cours -->
        <div class="current-order-info" id="current-order-info" style="display: none;">
            <!-- Sera rempli dynamiquement -->
        </div>

        <!-- Barre de recherche -->
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="Rechercher un produit...">
        </div>

        <!-- Catégories -->
        <div class="category-tabs">
            <div class="category-tab active" data-category="all">Tous</div>
            {% for category in categories %}
            <div class="category-tab" data-category="{{ category.id }}">
                {{ category.name }}
            </div>
            {% endfor %}
        </div>

        <!-- Grille de produits -->
        <div class="products-container">
            <div class="product-grid">
                {% for product in products %}
                <div class="product-btn" data-product-id="{{ product.id }}"
                     data-category="{{ product.category_id }}">
                    <div class="image-container">
                        {% if product.image_path %}
                        <img src="{{ url_for('static', filename=product.image_path) }}"
                             alt="{{ product.name }}">
                        {% else %}
                        <img src="{{ url_for('static', filename='img/default-product.png') }}"
                             alt="Image par défaut">
                        {% endif %}
                    </div>
                    <div class="product-name">{{ product.name }}</div>
                    <div class="price">{{ "%.2f"|format(product.price) }} €</div>
                    <div class="stock {% if product.get_stock_status() != 'in_stock' %}stock-warning{% endif %}">
                        {{ product.get_available_quantity()|round(1) }}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Panneau droit (panier) -->
    <div class="right-panel">
        {% include 'pos/_cart.html' %}
    </div>
</div>

<!-- Inclure les modals de sélection de service -->
{% include 'pos/service_selector.html' %}

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Fonction pour mettre à jour la grille de produits
    function updateProductGrid(products) {
        const grid = document.querySelector('.product-grid');
        grid.innerHTML = '';

        products.forEach(product => {
            const productElement = document.createElement('div');
            productElement.className = 'product-btn';
            productElement.dataset.productId = product.id;

            const stockStatus = product.stock_quantity <= product.minimum_stock ? 'stock-warning' : '';

            productElement.innerHTML = `
                <div class="image-container">
                    <img src="${product.image_path}" alt="${product.name}">
                </div>
                <div class="product-name">${product.name}</div>
                <div class="price">${product.price.toFixed(2)} €</div>
                <div class="stock ${stockStatus}">
                    ${product.stock_quantity.toFixed(1)}
                </div>
            `;

            grid.appendChild(productElement);
        });
    }

    // Gestionnaire de clic pour les onglets de catégorie
    document.querySelectorAll('.category-tab').forEach(tab => {
        tab.addEventListener('click', function() {
            // Retirer la classe active de tous les onglets
            document.querySelectorAll('.category-tab').forEach(t => t.classList.remove('active'));
            // Ajouter la classe active à l'onglet cliqué
            this.classList.add('active');

            // Récupérer l'ID de la catégorie
            const categoryId = this.dataset.category === 'all' ? 0 : parseInt(this.dataset.category);

            // Appeler l'API pour obtenir les produits de la catégorie
            fetch(`/pos/get_products/${categoryId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateProductGrid(data.data);
                    }
                })
                .catch(error => console.error('Erreur:', error));
        });
    });
});

// Fonction pour ouvrir le sélecteur de salles
function openRoomSelector() {
    // Ouvrir directement le modal de sélection de salle/table
    const modal = new bootstrap.Modal(document.getElementById('roomTableModal'));

    // Charger les salles si le service selector existe
    if (window.serviceSelector) {
        window.serviceSelector.selectedService = 'dine_in';
        window.serviceSelector.openRoomTableModal();
    } else {
        modal.show();
    }
}

// Fonction pour afficher les informations de commande
function showOrderInfo(orderData) {
    const orderInfoElement = document.getElementById('current-order-info');
    if (orderInfoElement && orderData) {
        const serviceName = getServiceDisplayName(orderData.service_type);
        let info = `<div class="order-details">`;
        info += `<i class="fas fa-utensils"></i>`;
        info += `<strong>${serviceName}</strong>`;

        if (orderData.table_id) {
            info += `<span>Table ${orderData.table_number || orderData.table_id}</span>`;
        }
        info += `<span>${orderData.covers_count} couvert(s)</span>`;
        info += `</div>`;

        info += `<div class="order-actions">`;
        info += `<button class="btn btn-outline-light btn-sm" onclick="changeTable()">`;
        info += `<i class="fas fa-exchange-alt"></i> Changer table`;
        info += `</button>`;
        info += `<button class="btn btn-outline-light btn-sm" onclick="clearOrder()">`;
        info += `<i class="fas fa-times"></i> Annuler`;
        info += `</button>`;
        info += `</div>`;

        orderInfoElement.innerHTML = info;
        orderInfoElement.style.display = 'flex';
    }
}

function getServiceDisplayName(serviceType) {
    const serviceNames = {
        'dine_in': 'Sur place',
        'takeaway': 'À emporter',
        'delivery': 'Livraison',
        'drive_thru': 'Service au volant'
    };
    return serviceNames[serviceType] || serviceType;
}

function changeTable() {
    openRoomSelector();
}

function clearOrder() {
    // Vider le panier et masquer les informations de commande
    if (window.cart) {
        window.cart.clear();
    }

    const orderInfoElement = document.getElementById('current-order-info');
    if (orderInfoElement) {
        orderInfoElement.style.display = 'none';
    }

    // Supprimer les données de session
    sessionStorage.removeItem('newOrderData');
}

// Vérifier s'il y a des données de commande au chargement
document.addEventListener('DOMContentLoaded', function() {
    const orderData = sessionStorage.getItem('newOrderData');
    if (orderData) {
        try {
            const parsedData = JSON.parse(orderData);
            showOrderInfo(parsedData);
        } catch (e) {
            console.error('Erreur lors du parsing des données de commande:', e);
        }
    }

    // Vérifier s'il y a une commande à éditer après l'initialisation de POS
    {% if edit_order %}
    console.log('Commande à éditer détectée:', {{ edit_order.id }});
    // Attendre que POS soit initialisé avant de charger la commande
    waitForPOSAndLoadOrder({{ edit_order.id }});
    {% endif %}
});

// Fonction pour attendre l'initialisation de POS et charger la commande
function waitForPOSAndLoadOrder(orderId) {
    let attempts = 0;
    const maxAttempts = 100; // 10 secondes max

    function checkPOSReady() {
        attempts++;
        console.log(`Tentative ${attempts}: Vérification de l'initialisation de POS...`);

        if (window.POS && window.POS.cart !== undefined) {
            console.log('POS initialisé, chargement de la commande...');
            loadExistingOrder(orderId);
        } else if (attempts < maxAttempts) {
            setTimeout(checkPOSReady, 100); // Réessayer dans 100ms
        } else {
            console.error('Timeout: POS non initialisé après 10 secondes');
            alert('Erreur: Le système POS n\'a pas pu s\'initialiser. Veuillez recharger la page.');
        }
    }

    checkPOSReady();
}

// Fonction pour charger une commande existante
async function loadExistingOrder(orderId) {
    try {
        console.log('Début du chargement de la commande:', orderId);

        // Vérifier que POS est bien initialisé
        if (!window.POS || window.POS.cart === undefined) {
            console.error('POS non initialisé au moment du chargement');
            alert('Erreur: Système POS non initialisé');
            return;
        }

        console.log('POS confirmé initialisé, récupération des détails...');

        // Récupérer les détails de la commande
        const response = await fetch(`/pos/api/get_order_details/${orderId}`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        if (!data.success) {
            throw new Error(data.error || 'Erreur inconnue');
        }

        const order = data.order;
        console.log('Commande récupérée:', order);

        // Vider le panier actuel
        window.POS.cart = [];
        console.log('Panier vidé');

        // Ajouter les articles au panier en utilisant la fonction POS existante
        let itemsAdded = 0;
        for (const item of order.items) {
            try {
                // Chercher le produit dans la liste
                const productElement = document.querySelector(`[data-product-id="${item.product_id}"]`);
                if (productElement) {
                    // Simuler un clic sur le produit pour chaque quantité
                    for (let i = 0; i < item.quantity; i++) {
                        productElement.click();
                        itemsAdded++;
                    }
                    console.log(`Ajouté ${item.quantity}x ${item.product_name}`);
                } else {
                    console.warn(`Produit ${item.product_id} (${item.product_name}) non trouvé dans l'interface`);
                    // Ajouter manuellement au panier si le produit n'est pas visible
                    for (let i = 0; i < item.quantity; i++) {
                        window.POS.cart.push({
                            id: item.product_id,
                            name: item.product_name,
                            price: item.unit_price
                        });
                        itemsAdded++;
                    }
                }
            } catch (itemError) {
                console.error(`Erreur lors de l'ajout de ${item.product_name}:`, itemError);
            }
        }

        // Forcer la mise à jour de l'affichage
        if (window.POS.updateCartDisplay) {
            window.POS.updateCartDisplay();
        } else if (window.updateCartDisplay) {
            window.updateCartDisplay();
        }

        // Afficher les informations de la commande
        if (order.table_id && order.table_number) {
            showOrderInfo({
                table_id: order.table_id,
                table_number: order.table_number,
                covers_count: order.covers_count || 1,
                service_type: order.service_type || 'dine_in'
            });
        }

        console.log(`Commande chargée avec succès: ${itemsAdded} article(s) ajouté(s)`);

        // Afficher un message de confirmation
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
        alertDiv.style.top = '20px';
        alertDiv.style.right = '20px';
        alertDiv.style.zIndex = '9999';
        alertDiv.innerHTML = `
            <strong>Commande chargée!</strong> ${itemsAdded} article(s) ajouté(s) au panier.
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(alertDiv);

        // Supprimer l'alerte après 5 secondes
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 5000);

    } catch (error) {
        console.error('Erreur lors du chargement de la commande:', error);
        alert(`Erreur lors du chargement de la commande: ${error.message}`);
    }
}
</script>
{% endblock %}